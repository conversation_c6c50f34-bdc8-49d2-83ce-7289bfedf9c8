
# SubsOrPurCounterFacadeService 设计文档

## 1. 模块名称

`SubsOrPurCounterFacadeService`

## 2. 模块目的

作为高端订单中心的柜台基金认/申购核心服务，提供统一的交易入口，处理来自柜台系统的基金购买请求。

## 3. 功能列表

| 方法名  | 核心用途                                                               |
| :------ | :--------------------------------------------------------------------- |
| `execute` | 接收并处理柜台基金认/申购请求，完成参数校验、业务处理、订单创建、信息持久化及消息发送等一系列操作。 |

## 4. 关键方法规范

### `execute(SubsOrPurCounterRequest request)`

-   **方法签名**:
    ```java
    public SubsOrPurCounterResponse execute(SubsOrPurCounterRequest request)
    ```
-   **参数**: `SubsOrPurCounterRequest`
    -   **描述**: 包含了柜台认/申购所需的所有信息。
    -   **关键字段示例**:
        -   `disCode` (String, 必填): 销售渠道代码，例如 `"0001"`。
        -   `txAcctNo` (String, 必填): 交易账号。
        -   `fundCode` (String, 必填): 基金代码。
        -   `buyList` (List<BuyBean>, 必填): 购买信息列表，包含购买金额等。
        -   `payList` (List<PayBean>, 必填): 支付信息列表，包含支付方式、支付金额等。
        -   `discountRate` (BigDecimal, 可选): 申购费率折扣。
        -   `isRedeemExpire` (String, 可选): 是否赎回时到期，`"1"` 是, `"0"` 否。
        -   `preExpireDate` (String, 可选): 预计到期日, 格式 `yyyyMMdd`。
-   **返回值**: `SubsOrPurCounterResponse`
    -   **描述**: 返回订单处理结果，主要包含订单号等关键信息。
    -   **关键字段示例**:
        -   `dealNo` (String): 交易订单号。
        -   `externalDealNo` (String): 外部交易订单号。
        -   `isSuccess` (boolean): 操作是否成功。
-   **异常**:
    -   `TmsBusinessException`: 在参数校验失败或业务规则不满足时抛出。
    -   其他运行时异常: 在数据库操作或消息发送失败时可能抛出。

## 5. 业务逻辑描述

该方法是幂等的（`@Idempotent`注解），确保重复请求不会造成重复下单。整体处理流程如下：

1.  **初始化上下文**: 创建 `OrderCreateContext` 对象，用于在整个处理流程中传递数据。
2.  **参数校验**:
    -   调用 `checkParamsExceptInst` 校验除机构投资者外的通用请求参数。
    -   调用 `validatePayList` 专门校验支付信息列表的有效性。
3.  **数据整合**: 调用 `combineRequestInfo` 将请求参数 `request` 中的信息整合到 `context` 中，并为购买清单 `buyList` 中的每个项目设置折扣率、是否到期等附加信息。
4.  **核心业务处理**: 调用 `subsOrPurCounterBusiProcess.process()` 方法，执行特定于柜台交易的复杂业务逻辑，如费用计算、风险匹配、资格校验等。
5.  **订单创建**:
    -   通过 `DisCodeCreateOrderLogicFactory` 根据分销代码 `disCode` 获取对应的订单创建逻辑处理器。
    -   调用该处理器的 `createOrder` 方法，基于上下文 `context` 创建订单实体 `OrderCreateBean`，其中包含了订单PO、支付单PO等。
6.  **数据持久化**: 调用 `dealCompositeRepository.saveOrder()` 将 `OrderCreateBean` 中的所有订单相关信息（如订单表、支付表）原子性地保存到数据库。
7.  **消息通知**:
    -   `sendPayMessage`: 发送支付消息到消息队列，触发后续支付流程。
    -   `sendDealActualMsg`: 发送订单实时状态消息，用于更新前端或其他系统。
    -   `sendCounterOrderMessage`: 如果投资者是机构或产品类型，则额外发送一条柜台机构下单消息。
8.  **构建并返回结果**: 调用 `createResponse` 方法，使用 `context` 和 `orderBean` 中的数据填充 `SubsOrPurCounterResponse` 对象并返回。

## 6. 流程图

```plantuml
@startuml
title SubsOrPurCounterFacadeService.execute() 流程图

start
:接收 SubsOrPurCounterRequest;
:创建 OrderCreateContext;
:checkParamsExceptInst(request)
检查核心参数;
:validatePayList(request)
校验支付信息列表;
:combineRequestInfo(request, context)
整合请求参数到Context;
:subsOrPurCounterBusiProcess.process(request, context)
执行业务逻辑处理;
:disCodeCreateOrderLogicFactory.getDisCodeCreateOrderCheckLogicProcess(disCode)
根据分销码获取订单创建逻辑处理器;
:disCodeCreateOrderCheckLogicProcess.createOrder(context)
创建订单(OrderCreateBean);
:dealCompositeRepository.saveOrder(orderBean)
保存订单及相关信息到数据库;
:sendPayMessage(paymentOrderPo)
发送支付消息;
:sendDealActualMsg(orderBean)
发送订单实时信息消息;
if (是机构或产品投资者?) then (yes)
  :subsOrPurCounterBusiProcess.sendCounterOrderMessage(...)
发送柜台机构下单消息;
endif
:createResponse(context, orderBean, response)
创建并填充返回结果;
:返回 SubsOrPurCounterResponse;
stop
@enduml
```

## 7. 序列图

```plantuml
@startuml
title 柜台认/申购序列图

actor "外部调用方" as caller
participant "SubsOrPurCounterFacadeService" as facade
participant "SubsOrPurCounterBusiProcess" as busiProcess
participant "DisCodeCreateOrderLogicFactory" as factory
participant "AbstractDisCodeCreateOrderLogicProcess" as createLogic
participant "DealCompositeRepository" as repository
participant "MessageQueue" as mq

caller -> facade: execute(request)
activate facade

facade -> facade: 参数校验和上下文准备
facade -> busiProcess: process(request, context)
activate busiProcess
busiProcess --> facade: 完成业务处理
deactivate busiProcess

facade -> factory: getDisCodeCreateOrderCheckLogicProcess(disCode)
activate factory
factory --> facade: 返回 createLogic 实例
deactivate factory

facade -> createLogic: createOrder(context)
activate createLogic
createLogic --> facade: 返回 OrderCreateBean
deactivate createLogic

facade -> repository: saveOrder(orderBean)
activate repository
repository --> facade: 订单保存成功
deactivate repository

facade -> mq: sendPayMessage(...)
facade -> mq: sendDealActualMsg(...)

alt 机构或产品投资者
    facade -> busiProcess: sendCounterOrderMessage(...)
    activate busiProcess
    busiProcess -> mq: 发送柜台下单消息
    deactivate busiProcess
end

facade -> facade: createResponse(...)
facade --> caller: 返回 SubsOrPurCounterResponse
deactivate facade

@enduml
```

## 8. 异常处理机制

-   **幂等性控制**: 方法入口处使用 `@Idempotent` 注解，防止因网络重试等原因导致的重复下单。底层通过拦截器实现，基于请求的关键参数生成唯一键并进行缓存检查。
-   **业务异常**: 在参数校验和核心业务处理（`subsOrPurCounterBusiProcess`）中，任何不符合业务规则的情况（如余额不足、产品已关闭、风险不匹配等）都会主动抛出 `TmsBusinessException`，中断流程并向调用方返回明确的错误信息。
-   **数据一致性**: 订单信息和支付信息的保存被封装在 `DealCompositeRepository.saveOrder()` 方法中，该方法应由事务保证其原子性，确保订单数据要么全部成功，要么全部失败。

## 9. 外部依赖和使用的公共模块

-   **`DisCodeCreateOrderLogicFactory`**: 工厂类，用于根据分销渠道代码动态选择合适的订单创建策略。
-   **`DealCompositeRepository`**: 数据仓库类，封装了所有与订单相关的数据库写操作，保证数据操作的原子性和一致性。
-   **`SubsOrPurCounterBusiProcess`**: 业务处理类，封装了柜台认/申购的核心业务规则和流程。
-   **`BaseSubsOrPurService`**: 服务基类，提供了参数校验、数据整合、消息发送和响应构建等公共能力。
-   **`Idempotent`**: 公共幂等组件，通过AOP实现接口的幂等性。
-   **MessageQueue (RocketMQ/ActiveMQ)**: 用于发送支付、订单状态等异步消息。

## 10. 调用此模块的模块

-   **外部柜台系统**: 通过 Dubbo RPC 协议调用此 Facade 接口，发起基金交易请求。
