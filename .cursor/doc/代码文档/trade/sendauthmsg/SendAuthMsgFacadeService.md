# SendAuthMsgFacadeService 接口设计文档

## 1. 接口名称
发送鉴权信息服务

## 2. 接口说明
该接口用于向用户发送各种类型的鉴权短信验证码，支持快捷支付鉴权、银行预留手机号修改鉴权、好买平台鉴权和e签宝鉴权等多种鉴权方式。主要应用于金融交易场景中的身份验证环节，确保交易安全性。

### 业务背景
在高端理财订单中心系统中，用户进行各种金融操作（如支付、签约、修改银行信息等）时需要进行身份验证，通过发送短信验证码到用户预留手机号来确认用户身份的真实性和操作的合法性。

### 使用场景
- 快捷支付前的身份验证
- 修改银行预留手机号时的验证
- 好买平台相关操作的身份确认
- e签宝电子签名前的身份验证

## 3. 接口类型
Dubbo 接口

## 4. 接口地址或方法签名
**接口类：** `com.howbuy.tms.high.orders.facade.trade.sendauthmsg.SendAuthMsgFacade`  
**方法签名：** `SendAuthMsgResponse execute(SendAuthMsgRequest request)`

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 发送鉴权信息标识ID | sendMsgReqId | String | 是 | "uuid-123456" | 唯一标识本次鉴权请求 |
| 鉴权信息类型 | authMsgType | String | 是 | "1" | 1-快捷鉴权，2-修改银行预留手机号，3-好买鉴权，4-e签宝鉴权 |
| 手机号是否存在 | mobileExit | boolean | 是 | true | 手机号在系统中是否存在的标识 |
| 银行代码 | bankCode | String | 否 | "305" | 银行机构代码 |
| 支持快捷鉴权 | supportQuickAuth | boolean | 否 | true | 是否支持快捷鉴权方式 |
| 证件类型 | idType | String | 否 | "0" | 证件类型，0-身份证 |
| 证件号码 | idNo | String | 否 | "110101199001011234" | 用户证件号码 |
| 手机号码 | mobile | String | 否 | "***********" | 用户手机号码 |
| 客户姓名 | custName | String | 否 | "张三" | 用户真实姓名 |
| 快捷鉴权参数 | quickCardAuthContextBean | QuickCardAuthContextBean | 否 | - | 快捷支付鉴权相关参数 |
| 修改手机号参数 | convenientVrifyForMobileBean | ConvenientVrifyForMobileBean | 否 | - | 修改银行预留手机号相关参数 |
| 好买鉴权参数 | messageCenterConterxtBean | MessageCenterConterxtBean | 否 | - | 好买消息中心发送鉴权参数 |

### 快捷鉴权参数详情 (QuickCardAuthContextBean)

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 鉴权方式 | authType | String | 否 | "SMS" | 鉴权方式类型 |
| 订单日期 | dealDate | Date | 否 | "2025-08-07" | 交易订单日期 |
| 交易账号 | txAcctNo | String | 是 | "**********" | 用户交易账号 |
| 银行代码 | bankCode | String | 是 | "305" | 银行机构代码 |
| 银行卡号 | bankAcct | String | 是 | "6226222019754686677" | 银行卡号 |
| 手机号码 | mobile | String | 是 | "***********" | 银行预留手机号 |
| 客户姓名 | custName | String | 是 | "张三" | 客户真实姓名 |
| 证件号码 | idNo | String | 是 | "110101199001011234" | 身份证号码 |
| 证件类型 | idType | String | 是 | "0" | 证件类型 |
| 结算商户代码 | lqdMerchCode | String | 否 | "MERCHANT001" | 结算商户代码 |
| 支付渠道 | pmtInstCode | String | 否 | "ALIPAY" | 支付机构代码 |
| 订单号 | dealNo | String | 否 | "ORDER123456" | 业务订单号 |

### 修改手机号参数详情 (ConvenientVrifyForMobileBean)

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 交易账号 | txAcctNo | String | 是 | "**********" | 用户交易账号 |
| 银行卡号 | bankAcct | String | 是 | "***************" | 银行卡号 |
| 手机号鉴权申请标志 | verifyFlag | String | 是 | "2" | 0-新增鉴权手机号，1-修改鉴权手机号 |
| 原手机号 | oldMobileNo | String | 否 | "***********" | 原银行预留手机号 |
| 新手机号 | mobileNo | String | 是 | "***********" | 新的手机号码 |

### 好买鉴权参数详情 (MessageCenterConterxtBean)

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 消息模板 | businessId | String | 是 | "10004" | 消息模板ID |
| 客户号 | custNo | String | 是 | "CUST001" | 客户编号 |
| 客户类型 | custType | Integer | 是 | 1 | 客户类型，1-个人客户 |
| 手机号 | mobile | String | 是 | "***********" | 接收短信的手机号 |
| 随机验证码 | random | String | 是 | "123456" | 6位随机验证码 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 返回代码 | returnCode | String | 是 | "000000" | 业务处理结果代码 |
| 描述信息 | description | String | 是 | "成功" | 业务处理结果描述 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 000000 | 成功 | 鉴权短信发送成功 |
| HIGH_ORDER_HOWBUY_MOBILE_NOT_EXIST | 好买手机号不存在 | 好买鉴权时手机号不存在于系统中 |
| 其他错误码 | 系统异常 | 具体错误信息见description字段 |

## 8. 关键业务逻辑说明

1. **参数校验阶段**：
   - 验证必填参数的完整性
   - 根据鉴权类型验证对应参数对象的完整性

2. **鉴权类型判断**：
   - 根据authMsgType字段判断具体的鉴权类型
   - 不同鉴权类型使用不同的参数对象和处理逻辑

3. **好买鉴权特殊处理**：
   - 当鉴权类型为好买鉴权(3)时，需要额外校验手机号是否在好买系统中存在
   - 如果手机号不存在(mobileExit=false)，直接返回错误

4. **异步任务处理**：
   - 创建SendAuthMsgTask任务对象
   - 将所有外部服务依赖注入到任务中
   - 启动异步线程执行实际的短信发送逻辑

5. **响应构建**：
   - 默认返回成功状态
   - 异常情况下返回对应的错误码和描述信息

## 9. 流程图

```plantuml
@startuml
start
:接收SendAuthMsgRequest请求;
:初始化SendAuthMsgResponse;
:设置默认成功返回码;

if (鉴权类型是否为好买鉴权?) then (是)
  if (手机号是否存在?) then (否)
    :设置错误返回码;
    :返回手机号不存在错误;
    stop
  endif
endif

:创建SendAuthMsgTask任务;
:注入外部服务依赖;
:启动异步任务线程;
:返回成功响应;
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "客户端" as Client
participant "SendAuthMsgFacadeService" as Facade
participant "SendAuthMsgTask" as Task
participant "快捷鉴权服务" as QuickAuth
participant "手机号修改服务" as MobileService
participant "好买消息中心" as HowbuyMsg
participant "e签宝服务" as ESign
participant "缓存服务" as Cache

Client -> Facade: execute(request)
Facade -> Facade: 参数校验
Facade -> Facade: 好买鉴权手机号校验

alt 校验失败
    Facade -> Client: 返回错误响应
else 校验成功
    Facade -> Task: 创建异步任务
    Facade -> Task: start()
    Facade -> Client: 返回成功响应
    
    Task -> Task: 根据鉴权类型选择服务
    
    alt 快捷鉴权
        Task -> QuickAuth: 调用快捷鉴权服务
        QuickAuth -> Task: 返回鉴权结果
    else 手机号修改
        Task -> MobileService: 调用手机号修改服务
        MobileService -> Task: 返回修改结果
    else 好买鉴权
        Task -> HowbuyMsg: 调用好买消息中心
        HowbuyMsg -> Task: 返回发送结果
    else e签宝鉴权
        Task -> ESign: 调用e签宝服务
        ESign -> Task: 返回鉴权结果
    end
    
    Task -> Cache: 保存鉴权结果到缓存
end
@enduml
```

## 11. 异常处理机制

### 主要异常场景及处理方式

1. **参数校验异常**：
   - 必填参数缺失：返回参数校验失败错误码
   - 参数格式错误：返回参数格式错误信息

2. **业务逻辑异常**：
   - 好买手机号不存在：返回HIGH_ORDER_HOWBUY_MOBILE_NOT_EXIST错误码
   - 鉴权类型不支持：记录错误日志，返回业务异常信息

3. **外部服务调用异常**：
   - 快捷鉴权服务异常：降级到好买短信通道
   - e签宝服务异常：降级到好买短信通道
   - 好买消息中心异常：记录错误日志，不影响主流程

4. **系统异常**：
   - 缓存服务异常：记录错误日志，不影响短信发送
   - 线程异常：记录详细异常信息，确保主线程正常返回

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| HighAuthCacheService | 高端鉴权缓存服务，用于存储鉴权流水号和验证码 |
| QuickCardAuthOuterOuterService | 快捷支付鉴权外部服务，处理银行快捷支付鉴权 |
| ConvenientVrifyForMobileOuterService | 银行预留手机号修改服务，处理手机号变更鉴权 |
| SendMessageOuterService | 好买消息中心服务，发送好买平台短信 |
| GetMobileVerificationCodeOuterService | e签宝手机验证码服务，获取e签宝鉴权验证码 |
| EncryptSingleOuterService | 单项加密服务，对敏感信息进行加密处理 |
| MessageSource | 消息资源服务，获取错误码对应的描述信息 |
| ExceptionCodes | 异常码常量类，定义系统标准错误码 |
| AuthMsgTypeEnum | 鉴权消息类型枚举，定义各种鉴权类型常量 |

## 13. 幂等性与安全性说明

### 幂等性
- **接口幂等性**：通过sendMsgReqId参数保证同一请求的幂等性
- **缓存机制**：相同的sendMsgReqId会覆盖之前的缓存结果
- **重复调用**：多次调用相同参数的请求，后续调用会覆盖前面的结果

### 安全性
- **数据加密**：手机号等敏感信息通过EncryptSingleOuterService进行加密
- **参数校验**：严格校验输入参数的合法性和完整性
- **权限控制**：通过Dubbo服务调用，依赖上层系统的权限控制
- **日志记录**：详细记录关键操作日志，便于审计和问题排查

### 限流与验签
- **限流机制**：依赖外部服务的限流策略
- **验签机制**：通过Dubbo框架保证服务调用的安全性
- **超时控制**：异步任务执行，不阻塞主线程响应

## 14. 业务处理涉及到的表名

| 表名 | 表注释 |
|------|--------|
| 无直接数据库操作 | 该接口主要调用外部服务，不直接操作数据库表 |

*注：该接口主要通过缓存和外部服务调用实现功能，不直接进行数据库操作。相关的数据持久化由各个外部服务负责处理。*

## 15. 备注与风险点

### 注意事项
1. **异步处理**：短信发送采用异步方式，接口响应不代表短信发送成功
2. **缓存依赖**：鉴权结果依赖缓存服务，缓存异常可能影响后续验证流程
3. **外部服务依赖**：多个外部服务依赖，任一服务异常都可能影响功能

### 边界处理
1. **鉴权类型扩展**：新增鉴权类型需要修改SendAuthMsgTask逻辑
2. **参数兼容性**：不同鉴权类型的参数对象需要保持向后兼容
3. **错误码统一**：所有错误码需要在ExceptionCodes中统一定义

### 特殊逻辑说明
1. **降级策略**：e签宝服务失败时自动降级到好买短信通道
2. **手机号校验**：好买鉴权类型需要额外校验手机号存在性
3. **加密处理**：好买消息中心发送前需要对手机号进行加密

### 风险点
1. **外部服务稳定性**：依赖多个外部服务，存在服务不可用风险
2. **缓存数据一致性**：缓存服务异常可能导致验证码校验失败
3. **异步任务监控**：异步执行缺乏实时监控，问题排查困难
4. **短信发送成功率**：不同通道的短信发送成功率可能存在差异

---

**文档版本**：1.0
**创建时间**：2025-08-07 12:02:39
**创建人**：hongdong.xie
**审核状态**：待审核
