# 接口详细设计文档：网上交易合并赎回接口 (V2 - 深度解析版)

## 1. 接口名称
网上交易合并赎回接口

## 2. 接口说明
**业务背景**：在高端产品交易场景中，客户可能需要同时赎回多笔资产（例如，同一基金的不同批次份额）。为提升用户体验和处理效率，后端提供此接口支持将多个单独的赎回申请合并到一个请求中进行处理。

**接口用途**：接收来自Web端（网上交易系统）的批量赎回请求，将多笔赎回操作合并处理，为每笔赎回创建独立的交易订单，并进行原子性保存。

**使用场景**：
- 客户在购物车或持仓列表中选择多笔份额，一次性发起赎回。
- 系统进行批量赎回操作。

## 3. 接口类型
Dubbo 接口

## 4. 接口地址或方法签名
- **接口类**: `com.howbuy.tms.high.orders.facade.trade.redeem.redeemmergeweb.RedeemMergeWebFacade`
- **方法签名**: 
  ```java
  RedeemMergeWebResponse execute(RedeemMergeWebRequest request);
  ```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 赎回请求列表 | `redeemList` | `List<RedeemBean>` | 是 | `[...]` | 包含一笔或多笔赎回信息的列表。 |
| 交易密码 | `txPwd` | `String` | 是 | `"******"` | 用户交易密码，用于身份验证和授权。 |
| 销售渠道代码 | `disCode` | `String` | 是 | `"HB"` | 标识请求来源的销售渠道，如"HB"代表好买。 |
| 赎回资金类型 | `redeemCapitalFlag` | `String` | 是 | `"0"` | 赎回资金用途标识，例如 "0" 代表普通赎回。 |
| ... | ... | ... | ... | ... | (继承自公共请求对象，包含客户号、操作员等) |

**`RedeemBean` 结构 (推断)**

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 基金代码 | `fundCode` | `String` | 是 | `"000123"` | 要赎回的基金代码。 |
| 赎回份额 | `shares` | `BigDecimal` | 是 | `1000.00` | 要赎回的基金份额。 |
| 交易账号 | `txAcctNo` | `String` | 是 | `"12345678"` | 客户的交易账号。 |
| 原申请单号 | `origAppNo` | `String` | 是 | `"2021022412345"` | 原始购买该份额的申请单号，用于指定赎回批次。 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 业务返回码 | `mBusiCode` | `String` | 是 | `"2003"` | 业务类型代码，"2003" 代表赎回业务。 |
| 交易订单号 | `dealNo` | `String` | 是 | `"H20210224..."` | 合并处理的第一笔订单的交易号。 |
| 外部交易单号 | `externalDealNo` | `String` | 是 | `"E20210224..."` | 合并处理的第一笔订单的外部系统关联号。 |
| 成功标识 | `isSuccess` | `boolean` | 是 | `true` | 接口调用是否成功。 |
| 返回码 | `rtnCode` | `String` | 是 | `"0000"` | 详细结果代码，"0000"表示成功。 |
| 返回信息 | `rtnMsg` | `String` | 是 | `"交易成功"` | 结果代码对应的描述信息。 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
| :--- | :--- | :--- |
| `0000` | 交易成功 | 表示所有赎回订单已成功创建。 |
| `9999` | 系统异常 | 通用系统错误，需要查看日志排查。 |
| `2xxx` | 业务校验失败 | 如交易密码错误、份额不足、产品不可赎回等。 |
| `3xxx` | 幂等性校验失败 | 检测到重复请求。 |

## 8. 关键业务逻辑说明（高级）

`execute`方法的执行可以分解为以下几个核心步骤，每个步骤都由专门的类和方法负责，体现了单一职责原则。

1.  **前置校验 (`validateRedeemListSize`)**: 在 `BaseRedeemService` 中实现。检查赎回列表 `redeemList` 是否为空，以及列表大小是否超过系统设定的阈值（如200笔），防止单次请求过大导致性能问题。

2.  **上下文构建 (`combineRequestInfo`)**: 在 `BaseRedeemService` 中实现。创建一个 `OrderCreateContext` 实例，它将作为整个交易流程的数据载体。此方法将请求对象 `RedeemMergeWebRequest` 中的客户信息、渠道信息以及 `redeemList` 转换为内部业务对象并填充到 `context` 中。

3.  **核心业务处理 (`redeemWebBusiProcess.process`)**: 这是业务规则校验的核心。`RedeemWebBusiProcess` 类负责执行所有与赎回相关的业务检查。它会遍历 `context` 中的每一笔赎回申请，并执行一系列校验，例如：
    -   调用外部服务验证交易密码的正确性。
    -   查询客户持仓，确认赎回的份额是否足额、可用。
    -   检查基金状态，确认基金是否开放赎回。
    -   检查客户账户状态是否正常。
    -   执行其他业务规则，如赎回频率限制、黑名单校验等。
    -   所有校验通过后，将计算出的费用、确认份额等信息写回 `context`。

4.  **订单创建 (`createMergeOrders`)**: 
    -   首先，`DisCodeCreateOrderLogicFactory` 根据请求中的 `disCode`（销售渠道）返回一个具体的 `AbstractDisCodeCreateOrderLogicProcess` 实现类。这是一个工厂模式的应用，允许系统为不同渠道（如直销、代销）定义不同的订单创建逻辑。
    -   然后，调用该实现类的 `createMergeOrders` 方法。此方法遍历 `context` 中经过业务处理的赎回列表，为每一项生成一个 `OrderCreateBean`。`OrderCreateBean` 是一个数据容器，包含了创建一笔订单所需的所有数据实体，如 `DealOrderPo` (订单主表)、`DealFeePo` (费用表)等。

5.  **数据持久化 (`dealCompositeRepository.saveMergeOrders`)**: `DealCompositeRepository` 负责数据库操作。`saveMergeOrders` 方法接收 `OrderCreateBean` 列表，并在一个数据库事务（`@Transactional`）中，遍历列表，将每个 `OrderCreateBean` 中的数据实体（`DealOrderPo` 等）批量插入到相应的数据库表中。事务确保了这批订单的原子性：要么全部成功写入，要么在任何一笔失败时全部回滚。

6.  **消息通知 (`sendDealActualMsg`)**: 在 `BaseRedeemService` 中实现。订单成功保存后，调用此方法将第一笔订单（`orderList.get(0)`）的核心信息封装成消息，发送到消息队列（MessageQueue）。这一步是异步的，用于通知下游系统（如风控、数据统计、通知中心）有新的交易产生。

7.  **响应构建 (`createResponse`)**: 在 `BaseRedeemService` 中实现。使用第一笔订单的信息和上下文数据，构建最终的 `RedeemMergeWebResponse` 对象，设置成功状态和相关订单号，然后返回给调用方。

## 9. 流程图（详细版）

```plantuml
@startuml
title 网上交易合并赎回 - 整体流程

start
:接收 RedeemMergeWebRequest;

partition BaseRedeemService {
  :validateRedeemListSize(request)
  校验赎回列表大小;
  :combineRequestInfo(request, context)
  准备订单创建上下文;
}

partition RedeemWebBusiProcess {
  :process(request, context, txPwd)
  执行核心业务处理;
  note right
    - 验证交易密码
    - 校验账户、产品、份额状态
    - 计算费用
  end note
}

partition "Create Order Logic" {
    :DisCodeCreateOrderLogicFactory.get(...)
    根据渠道获取订单创建器;
    :AbstractDisCodeCreateOrderLogicProcess.createMergeOrders(context)
    批量创建订单(List<OrderCreateBean>);
}

partition DealCompositeRepository {
    :saveMergeOrders(orderList)
    **事务**：批量保存订单至数据库;
}

partition BaseRedeemService {
    :sendDealActualMsg(orderList.get(0))
    发送第一笔订单的实时消息;
    :createResponse(...)
    构建并填充Response对象;
}

:返回 RedeemMergeWebResponse;
stop

@enduml
```

## 10. 时序图（详细版）

```plantuml
@startuml
title 网上交易合并赎回 - 详细时序图

actor "Web端" as web
participant "RedeemMergeWebFacadeService" as facade
participant "BaseRedeemService" as base
participant "RedeemWebBusiProcess" as busiProcess
participant "DisCodeCreateOrderLogicFactory" as factory
participant "AbstractDisCodeCreateOrderLogicProcess" as createLogic
participant "DealCompositeRepository" as repository
database "MySQL" as db
participant "MessageQueue" as mq

web -> facade: execute(request)
activate facade

facade -> base: validateRedeemListSize(request)
activate base
base --> facade
deactivate base

facade -> base: combineRequestInfo(request, context)
activate base
base --> facade
deactivate base

facade -> busiProcess: process(context, txPwd)
activate busiProcess
loop 对每笔赎回
    busiProcess -> db: 查询持仓、账户、产品信息
    busiProcess -> busiProcess: 执行业务规则校验
end
busiProcess --> facade: 业务校验与计算完成
deactivate busiProcess

facade -> factory: getDisCodeCreateOrderCheckLogicProcess(disCode)
activate factory
factory --> facade: 返回 createLogic 实例
deactivate factory

facade -> createLogic: createMergeOrders(context)
activate createLogic
loop 对每笔赎回
    createLogic -> createLogic: new OrderCreateBean()
    createLogic -> createLogic: 填充 DealOrderPo, DealFeePo, ...
end
createLogic --> facade: 返回 List<OrderCreateBean>
deactivate createLogic

facade -> repository: saveMergeOrders(orderList)
activate repository
repository -> db: **BEGIN TRANSACTION**
loop 对每个 OrderCreateBean
    repository -> db: INSERT INTO tms_deal_order, ...
end
repository -> db: **COMMIT**
repository --> facade: 保存成功
deactivate repository

facade -> base: sendDealActualMsg(orderList.get(0))
activate base
base -> mq: 发送订单消息
base --> facade
deactivate base

facade -> base: createResponse(...)
activate base
base --> facade: 返回 response 对象
deactivate base

facade --> web: RedeemMergeWebResponse
deactivate facade

@enduml
```

## 11. 异常处理机制
- **业务异常**：在 `redeemWebBusiProcess` 中，任何业务规则校验失败（如密码错误、份额不足）都会抛出 `TmsBusinessException`，中断流程，并由框架统一处理后返回给调用方具体的错误码和信息。
- **数据一致性**：`dealCompositeRepository.saveMergeOrders` 方法通过Spring的 `@Transactional` 注解保证了多笔订单的数据库插入操作在同一个事务中，确保了数据的一致性。
- **幂等性异常**：如果 `@Idempotent` 组件检测到重复请求，会直接中断执行并抛出特定异常，防止重复处理。

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
| :--- | :--- |
| `DisCodeCreateOrderLogicFactory` | 工厂类，根据分销渠道代码动态获取对应的订单创建逻辑处理器。 |
| `DealCompositeRepository` | 数据仓库层，封装了对交易相关数据表（如订单表）的原子性批量写操作。 |
| `RedeemWebBusiProcess` | 业务处理层，封装了网上交易赎回的核心业务规则校验逻辑。 |
| `BaseRedeemService` | 服务基类，提供了赎回业务线通用的方法，如参数校验、响应构建、消息发送等。 |
| `Idempotent` (AOP) | 幂等性控制组件，通过注解和拦截器防止接口重复调用。 |
| `MessageQueue` | 消息队列服务（如RocketMQ），用于发送订单实时信息，实现系统解耦。 |

## 13. 幂等性与安全性说明
- **幂等性**：接口通过 `@Idempotent` 注解实现幂等性。框架会根据请求的唯一标识（如根据用户ID、请求序列号等生成的key）在一定时间内阻止重复的请求被处理。
- **安全性**：
    - **鉴权**：Dubbo框架层面会有基础的服务调用鉴权。
    - **业务授权**：接口内部通过校验 `txPwd` (交易密码) 来确保操作者有权执行赎回。
    - **数据安全**：敏感信息（如密码）在传输和处理过程中应加密。

## 14. 业务处理涉及到的表名
| 表名 | 表注释 |
| :--- | :--- |
| `tms_deal_order` | 交易订单表 |
| `tms_payment_order` | 支付订单表 |
| `tms_deal_fee` | 交易费用表 |

## 15. 备注与风险点
- **风险点**：`sendDealActualMsg` 当前只发送了合并订单列表中的第一笔订单信息。如果下游系统需要感知每一笔订单的创建，这里的逻辑需要调整为遍历并发送所有订单的消息。
- **性能考虑**：`validateRedeemListSize` 方法限制了单次合并的笔数，这是为了防止单次请求数据量过大，对数据库和系统造成压力。此限制应根据业务量和系统性能进行评估和调整。