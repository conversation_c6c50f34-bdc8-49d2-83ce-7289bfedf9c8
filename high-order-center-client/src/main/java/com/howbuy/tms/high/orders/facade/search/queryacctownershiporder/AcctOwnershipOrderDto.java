package com.howbuy.tms.high.orders.facade.search.queryacctownershiporder;

import com.howbuy.tms.common.enums.busi.OwnershipTransferIdentityEnum;
import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:用户股权订单信息
 * @Author: yun.lu
 * Date: 2023/8/16 15:43
 */
@Data
public class AcctOwnershipOrderDto extends BaseDto {
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 产品code
     */
    private String fundCode;

    /**
     * 中台业务编码
     */
    private String mBusinessCode;

    /**
     * 确认日期
     */
    private String ackDt;


    /**
     * 确认份额
     */
    private BigDecimal ackVol;

    /**
     * 确认金额
     */
    private BigDecimal ackAmt;

    /**
     * 费用
     */
    private BigDecimal fee;

    /**
     * 转让价格
     */
    private BigDecimal transferPrice;

    /**
     * 转让标识
     * @see OwnershipTransferIdentityEnum
     */
    private String ownershipTransferIdentity;


}
