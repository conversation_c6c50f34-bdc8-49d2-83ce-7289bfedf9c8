package com.howbuy.tms.high.orders.facade.search.queryacctconfirmbalance;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Data;

/**
 * @Description:查询确认持仓请求
 * @Author: yun.lu
 * Date: 2023/12/18 15:06
 */
@Data
public class QueryAcctConfirmBalanceRequest extends OrderSearchBaseRequest {

    /**
     * 产品编码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "产品编码", isRequired = true)
    private String fundCode;
}
