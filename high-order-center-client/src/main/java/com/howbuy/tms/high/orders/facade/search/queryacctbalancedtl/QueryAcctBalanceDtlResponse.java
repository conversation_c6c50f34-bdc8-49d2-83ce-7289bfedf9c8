/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Description:查询高端产品持仓明细接口返回结果
 * 
 * @reason:
 * <AUTHOR>
 * @date 2017年4月11日 上午9:41:26
 * @since JDK 1.7
 */
public class QueryAcctBalanceDtlResponse extends OrderSearchBaseResponse {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = -6989971807357099927L;
    
    private List<BalanceDtlBean> balanceDtlList = new ArrayList<BalanceDtlBean>();
    /**
     * 赎回待确认笔数
     */
    private Integer redeemUnconfirmedNum;

    public List<BalanceDtlBean> getBalanceDtlList() {
        return balanceDtlList;
    }

    public void setBalanceDtlList(List<BalanceDtlBean> balanceDtlList) {
        this.balanceDtlList = balanceDtlList;
    }

    public Integer getRedeemUnconfirmedNum() {
        return redeemUnconfirmedNum;
    }

    public void setRedeemUnconfirmedNum(Integer redeemUnconfirmedNum) {
        this.redeemUnconfirmedNum = redeemUnconfirmedNum;
    }

    public static class BalanceDtlBean implements Serializable {

        /**
         * serialVersionUID:TODO（用一句话描述这个变量表示什么）
         *
         * @since Ver 1.1
         */

        private static final long serialVersionUID = -6776763701201056826L;
        
        /**
         * 交易账号
         */
        private String txAcctNo;
        /**
         * 分销机构号
         */
        private String disCode;

        /**
         * 产品代码
         */
        private String productCode;
        /**
         * 份额类型
         */
        private String fundShareClass;
        /**
         * 产品名称
         */
        private String productName;
        /**
         * 产品类型
         */
        private String productType;

        /**
         * 净值
         */
        private BigDecimal nav;
        /**
         * 产品状态：0-交易； 1-发行； 2-发行成功； 3-发行失败； 4-停止交易； 5-停止申购； 6-停止赎回； 7-权益登记； 8-红利发放；
         * 9-基金封闭； a-基金终止
         */
        private String productStatus;
        /**
         * 净值日期
         */
        private String navDt;
        /**
         * 购买状态：1-认购，2-申购，3-不可购买
         */
        private String buyStatus;
        /**
         * 赎回状态 1-可赎回，2-不可赎回
         */
        private String redeemStatus;
        /**
         * 协议号
         */
        private String protocolNo;
        /**
         * 资金账号
         */
        private String cpAcctNo;
        /**
         * 银行代码
         */
        private String bankCode;
        /**
         * 银行名称
         */
        private String bankName;
        /**
         * 银行卡号
         */
        private String bankAcctNo;
        /**
         * 银行卡号掩码
         */
        private String bankAcctMask;
        /**
         * 总份额
         */
        private BigDecimal balanceVol;
        /**
         * 可用份额
         */
        private BigDecimal availVol;
        /**
         * 待确认份额
         */
        private BigDecimal unconfirmedVol;
        /**
         * 市值
         */
        private BigDecimal marketValue;
        /**
         * 开放赎回日期
         */
        private String openRedeDt;
        
        /**
         * 产品通道  3-群济私募 5-好买公募 6-高端公募
         */
        private String productChannel;

        /**
         * 总持有份额
         */
        private BigDecimal totalVol;

        /**
         * 总在途份额
         */
        private BigDecimal totalUnConfirmVol;
        /**
         * 总可用份额
         */
        private BigDecimal totalAvailVol;
        /**
         * 全赎剩余份额
         */
        private BigDecimal redeemAllSurplusVol;
        /**
         * 总全赎剩余份额
         */
        private BigDecimal totalRedeemAllSurplusVol;

        public String getBankAcctMask() {
            return bankAcctMask;
        }

        public void setBankAcctMask(String bankAcctMask) {
            this.bankAcctMask = bankAcctMask;
        }

        public String getOpenRedeDt() {
            return openRedeDt;
        }

        public void setOpenRedeDt(String openRedeDt) {
            this.openRedeDt = openRedeDt;
        }

        public String getProtocolNo() {
            return protocolNo;
        }

        public void setProtocolNo(String protocolNo) {
            this.protocolNo = protocolNo;
        }

        public String getCpAcctNo() {
            return cpAcctNo;
        }

        public void setCpAcctNo(String cpAcctNo) {
            this.cpAcctNo = cpAcctNo;
        }

        public String getBankCode() {
            return bankCode;
        }

        public void setBankCode(String bankCode) {
            this.bankCode = bankCode;
        }

        public String getBankName() {
            return bankName;
        }

        public void setBankName(String bankName) {
            this.bankName = bankName;
        }

        public BigDecimal getBalanceVol() {
            return balanceVol;
        }

        public void setBalanceVol(BigDecimal balanceVol) {
            this.balanceVol = balanceVol;
        }

        public BigDecimal getUnconfirmedVol() {
            return unconfirmedVol;
        }

        public void setUnconfirmedVol(BigDecimal unconfirmedVol) {
            this.unconfirmedVol = unconfirmedVol;
        }

        public BigDecimal getMarketValue() {
            return marketValue;
        }

        public void setMarketValue(BigDecimal marketValue) {
            this.marketValue = marketValue;
        }

        public BigDecimal getAvailVol() {
            return availVol;
        }

        public void setAvailVol(BigDecimal availVol) {
            this.availVol = availVol;
        }

        public String getBankAcctNo() {
            return bankAcctNo;
        }

        public void setBankAcctNo(String bankAcctNo) {
            this.bankAcctNo = bankAcctNo;
        }

        public String getTxAcctNo() {
            return txAcctNo;
        }

        public void setTxAcctNo(String txAcctNo) {
            this.txAcctNo = txAcctNo;
        }

        public String getDisCode() {
            return disCode;
        }

        public void setDisCode(String disCode) {
            this.disCode = disCode;
        }

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public String getFundShareClass() {
            return fundShareClass;
        }

        public void setFundShareClass(String fundShareClass) {
            this.fundShareClass = fundShareClass;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public String getProductType() {
            return productType;
        }

        public void setProductType(String productType) {
            this.productType = productType;
        }

        public BigDecimal getNav() {
            return nav;
        }

        public void setNav(BigDecimal nav) {
            this.nav = nav;
        }

        public String getProductStatus() {
            return productStatus;
        }

        public void setProductStatus(String productStatus) {
            this.productStatus = productStatus;
        }

        public String getNavDt() {
            return navDt;
        }

        public void setNavDt(String navDt) {
            this.navDt = navDt;
        }

        public String getBuyStatus() {
            return buyStatus;
        }

        public void setBuyStatus(String buyStatus) {
            this.buyStatus = buyStatus;
        }

        public String getRedeemStatus() {
            return redeemStatus;
        }

        public void setRedeemStatus(String redeemStatus) {
            this.redeemStatus = redeemStatus;
        }

        public String getProductChannel() {
            return productChannel;
        }

        public void setProductChannel(String productChannel) {
            this.productChannel = productChannel;
        }

        public BigDecimal getTotalVol() {
            return totalVol;
        }

        public void setTotalVol(BigDecimal totalVol) {
            this.totalVol = totalVol;
        }

        public BigDecimal getTotalUnConfirmVol() {
            return totalUnConfirmVol;
        }

        public void setTotalUnConfirmVol(BigDecimal totalUnConfirmVol) {
            this.totalUnConfirmVol = totalUnConfirmVol;
        }

        public BigDecimal getTotalAvailVol() {
            return totalAvailVol;
        }

        public void setTotalAvailVol(BigDecimal totalAvailVol) {
            this.totalAvailVol = totalAvailVol;
        }

        public BigDecimal getRedeemAllSurplusVol() {
            return redeemAllSurplusVol;
        }

        public void setRedeemAllSurplusVol(BigDecimal redeemAllSurplusVol) {
            this.redeemAllSurplusVol = redeemAllSurplusVol;
        }

        public BigDecimal getTotalRedeemAllSurplusVol() {
            return totalRedeemAllSurplusVol;
        }

        public void setTotalRedeemAllSurplusVol(BigDecimal totalRedeemAllSurplusVol) {
            this.totalRedeemAllSurplusVol = totalRedeemAllSurplusVol;
        }
    }
}

