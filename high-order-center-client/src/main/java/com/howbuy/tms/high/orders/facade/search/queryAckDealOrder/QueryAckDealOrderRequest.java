package com.howbuy.tms.high.orders.facade.search.queryAckDealOrder;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Data;

import java.util.List;

/**
 * @Description:查询已确定订单
 * @Author: yun.lu
 * Date: 2024/12/26 11:11
 */
@Data
public class QueryAckDealOrderRequest extends OrderSearchBaseRequest {
    /**
     * 订单明细
     */
    private List<String> fundCodeList;

    /**
     * 上报日期或者确认开始日期,yyyyMMdd
     */
    private String submitOrAckStartDt;

    /**
     * 上报日期或者确认结束日期,yyyyMMdd
     */
    private String submitOrAckEndDt;
}
