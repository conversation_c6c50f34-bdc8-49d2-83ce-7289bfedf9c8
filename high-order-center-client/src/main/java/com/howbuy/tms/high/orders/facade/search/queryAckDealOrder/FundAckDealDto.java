package com.howbuy.tms.high.orders.facade.search.queryAckDealOrder;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.Set;

/**
 * @Description:产品维度确认订单信息
 * @Author: yun.lu
 * Date: 2024/12/26 11:17
 */
@Data
public class FundAckDealDto extends BaseDto {
    /**
     * 产品编码
     */
    private String fundCode;
    /**
     * 购买订单号列表
     */
    private Set<String> buyDealNoList;
    /**
     * 赎回订单号列表
     */
    private Set<String> reDeemDealNoList;
    /**
     * 购买订单数量
     */
    private int buyCount = 0;
    /**
     * 赎回订单数量
     */
    private int redeemCount = 0;


    public boolean hasData() {
        return !CollectionUtils.isEmpty(buyDealNoList) || !CollectionUtils.isEmpty(reDeemDealNoList);
    }

    public void addBuy(String dealNo) {
        buyDealNoList.add(dealNo);
        buyCount = buyDealNoList.size();
    }

    public void addRedeem(String dealNo) {
        reDeemDealNoList.add(dealNo);
        redeemCount = reDeemDealNoList.size();
    }

}
