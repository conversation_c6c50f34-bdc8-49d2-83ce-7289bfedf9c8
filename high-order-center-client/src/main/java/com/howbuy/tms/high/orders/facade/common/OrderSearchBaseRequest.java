/**
 *Copyright (c) 2016, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.common;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.validate.MyValidation;

/**
 * @description:(查询基础请求类)
 * @reason:
 * <AUTHOR>
 * @date 2017年4月11日 下午1:48:40
 * @since JDK 1.7
 */

/**
 * @apiDefine orderSearchBaseRequest 共有请求参数(req)
 * @apiGroup high-order-center
 * 
 * @apiParam {String} [txAcctNo] 交易账号
 * @apiParam {String} [hbOneNo] 一账通账号
 * 
 */
public class OrderSearchBaseRequest extends OrderBaseRequest {

    private static final long serialVersionUID = 4665383521827243789L;
    
    /**
     * 交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易账号", isRequired = false, max = 10)
    private String txAcctNo;
    
    /**
     * 一账通账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通账号", isRequired = false, max = 10)
    private String hbOneNo;
    
    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getHbOneNo() {
        return hbOneNo;
    }

    public void setHbOneNo(String hbOneNo) {
        this.hbOneNo = hbOneNo;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }
    
}
