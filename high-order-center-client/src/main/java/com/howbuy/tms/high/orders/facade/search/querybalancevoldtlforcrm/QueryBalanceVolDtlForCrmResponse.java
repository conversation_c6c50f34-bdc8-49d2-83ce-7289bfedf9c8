/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.bean.BalanceVolDtlBean;

import java.util.List;

/**
 * crm查询份额明细
 * <AUTHOR>
 * @date 2021/5/13 16:31
 * @since JDK 1.8
 */
public class QueryBalanceVolDtlForCrmResponse extends OrderSearchBaseResponse {

    private static final long serialVersionUID = -4171429127320388826L;

    private List<BalanceVolDtlBean> balanceVolDtlList;

    public List<BalanceVolDtlBean> getBalanceVolDtlList() {
        return balanceVolDtlList;
    }

    public void setBalanceVolDtlList(List<BalanceVolDtlBean> balanceVolDtlList) {
        this.balanceVolDtlList = balanceVolDtlList;
    }
}