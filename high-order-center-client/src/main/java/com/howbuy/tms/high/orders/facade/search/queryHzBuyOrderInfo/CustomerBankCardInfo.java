package com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Data;

/**
 * @Description:用户银行卡信息
 * @Author: yun.lu
 * Date: 2023/11/23 20:33
 */
@Data
public class CustomerBankCardInfo extends BaseDto {

    /**
     *  银行编号
     */
    private String bankCode;

    /**
     *  银行卡号掩码
     */
    private String bankAcctMask;

    /**
     * 分行名称
     */
    private String bankRegionName;

    /**
     *  银行名称
     */
    private String bankName;


    /**
     * 银行图标地址
     */
    private String bankLogoUrl;

    /**
     * 资金账号
     */
    private String cpAcctNo;
}
