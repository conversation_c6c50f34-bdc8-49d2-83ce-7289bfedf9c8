package com.howbuy.tms.high.orders.facade.search.queryOnWayOrder;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Data;

/**
 * @Description:用户在途持仓
 * @Author: yun.lu
 * Date: 2023/11/27 15:10
 */
@Data
public class OnWayAgentDealDtlInfo extends BaseDto {
    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 产品编码
     */
    private String fundCode;


    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 订单明细号
     */
    private String dealDtlNo;

    /**
     * 预约订单号
     */
    private String appointmentDealNo;

}
