package com.howbuy.tms.high.orders.facade.search.queryCustomerRedeemAppointInfo;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Data;

import java.util.List;

/**
 * @Description:查询用户赎回日历信息结果
 * @Author: yun.lu
 * Date: 2024/10/9 16:32
 */
@Data
public class QueryCustomerRedeemAppointInfoResponse extends OrderSearchBaseResponse {
    /**
     * 用户赎回日历信息列表
     */
    private List<CustomerRedeemAppointInfo> customerRedeemAppointInfoList;
}
