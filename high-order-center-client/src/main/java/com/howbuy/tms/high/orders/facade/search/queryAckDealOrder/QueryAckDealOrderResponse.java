package com.howbuy.tms.high.orders.facade.search.queryAckDealOrder;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Data;

import java.util.List;

/**
 * @Description:查询确认订单信息返回对象
 * @Author: yun.lu
 * Date: 2024/12/26 11:11
 */
@Data
public class QueryAckDealOrderResponse extends OrderSearchBaseResponse {
    /**
     * 产品维度订单信息
     */
    private List<FundAckDealDto> fundAckDealDtoList;
}
