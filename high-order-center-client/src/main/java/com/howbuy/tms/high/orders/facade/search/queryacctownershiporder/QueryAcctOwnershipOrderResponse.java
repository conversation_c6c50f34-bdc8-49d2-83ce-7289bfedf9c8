package com.howbuy.tms.high.orders.facade.search.queryacctownershiporder;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Data;

import java.util.List;

/**
 * @Description:查询用户股权订单信息结果
 * @Author: yun.lu
 * Date: 2023/8/16 15:35
 */
@Data
public class QueryAcctOwnershipOrderResponse extends OrderSearchBaseResponse {
    /**
     * 转让明细
     */
    private List<AcctOwnershipOrderDto> acctOwnershipOrderDtoList;


    /**
     * 币种
     */
    private String currency;


}
