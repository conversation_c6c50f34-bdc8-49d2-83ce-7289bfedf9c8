/**
 *Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.common;

import com.howbuy.tms.common.client.BaseRequest;

/**
 * @apiDefine orderBaseRequest 共有请求参数(req)
 * @apiGroup high-order-center
 * 
 * @apiParam {String} disCode  分销机构代码
 * @apiParam {String} outletCode  网点代码
 * @apiParam {String} [appDt]  申请日期(yyyyMMdd)
 * @apiParam {String} [appTm]  申请时间(HHMMSS)
 * @apiParam {String} operIp  交易Ip
 * @apiParam {String} [txCode]  交易码(有默认值，无需设置)
 * @apiParam {String} txChannel  交易渠道<br>1-柜台;2-网站;3-电话;4-Wap;5-App
 * @apiParam {String} dataTrack  数据跟踪
 * 
 */

public class OrderBaseRequest extends BaseRequest {

    private static final long serialVersionUID = 4665983527827243789L;
    
    /**
     * 
     * getShortAppTm:将时间的最后一位去掉，例如：121025-->12102(由于在极端情况下去掉最后一位时间是会错误拦截，
     * 所以目前直接返回申请时间)
     * 
     * @return
     * @return String
     * <AUTHOR>
     * @date 2016年10月26日 下午9:12:20
     */
    protected String getShortAppTm(){
        return getAppTm();
    }

}
