package com.howbuy.tms.high.orders.facade.search.queryBalanceTxAcctNoByFund;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Data;

import java.util.List;

/**
 * @Description:根据产品查询持仓用户交易账号结果,注意,需要将交易账号与一账通结合起来看,因为代销的返回的交易账号,直销的返回一账通
 * @Author: yun.lu
 * Date: 2024/10/22 14:20
 */
@Data
public class QueryBalanceTxAcctNoByFundResponse extends OrderSearchBaseResponse {
    /**
     * 交易账号集合
     */
    private List<String> txAcctNoList;
    /**
     * 一账通列表
     */
    private List<String> hbOneNoList;
}
