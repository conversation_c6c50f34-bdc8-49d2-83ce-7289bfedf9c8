package com.howbuy.tms.high.orders.facade.search.queryLiCaiResult;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Data;

/**
 * @Description:查询理财分析结果
 * @Author: yun.lu
 * Date: 2024/12/9 14:44
 */
@Data
public class QueryLiCaiResultResponse extends OrderSearchBaseResponse {
    /**
     * 是否展示理财分析,1:展示,0:不展示
     */
    private String showLiCai;
    /**
     * 是否阐释理财分析原因
     */
    private String showLiCaiReason;
}
