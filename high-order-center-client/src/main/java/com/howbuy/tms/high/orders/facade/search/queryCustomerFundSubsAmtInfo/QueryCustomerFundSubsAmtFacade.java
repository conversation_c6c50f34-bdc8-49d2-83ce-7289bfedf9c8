package com.howbuy.tms.high.orders.facade.search.queryCustomerFundSubsAmtInfo;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * @Description:查询用户产品认缴信息
 * 已持有的好臻分销且分次call产品且未实缴完成（实缴金额小于认缴金额），包括：客户号、客户姓名、基金代码、基金简称、认缴金额（千分位展示）、持仓份额（千分位展示）
 * @Author: yun.lu
 * Date: 2024/7/15 18:49
 */
public interface QueryCustomerFundSubsAmtFacade extends BaseFacade<QueryCustomerFundSubsAmtRequest, QueryCustomerFundSubsAmtResponse> {

}
