package com.howbuy.tms.high.orders.facade.search.queryasset;

import java.math.BigDecimal;

public class HighFundAssetIncomeDomain {
    private BigDecimal currentAsset; //当前收益
    private BigDecimal currentAssetRmb; //当前收益（人民币）
    private BigDecimal balanceCost; //持仓总成本
    private BigDecimal balanceCostRmb; //持仓总成本（人民币）
    private BigDecimal dailyAsset; //日收益
    private BigDecimal dailyAssetRmb; //日收益（人民币）
    private BigDecimal accumIncome; //累计收益
    private BigDecimal accumIncomeRmb; //累计收益(人民币)
    private BigDecimal accumRealizedIncome; //累计已实现收益
    private BigDecimal accumRealizedIncomeRmb; //累计已实现收益(人民币)
    private BigDecimal accumCost; //累计成本
    private BigDecimal accumCostRmb; //累计成本(人民币)
    private BigDecimal balanceAmt; //参考市值
    private BigDecimal balanceAmtRmb; //参考市值(人民币)
    private BigDecimal receivManageFee; //na管理费
    private BigDecimal receivPreformFee; //na业绩费
    private BigDecimal balanceIncomeNew; //持仓收益新
    private BigDecimal balanceIncomeNewRmb; //持仓收益新(人民币)
    private BigDecimal accumIncomeNew; //累计收益新
    private BigDecimal accumIncomeNewRmb; //累计收益新(人民币)
    private BigDecimal balanceFloatIncome; //持仓浮盈亏
    private BigDecimal balanceFloatIncomeRmb; //持仓浮盈亏(人民币)
    private BigDecimal balanceFloatIncomeRate; //持仓浮盈亏率
    private BigDecimal dayAssetRate; //最新收益率(日收益率)
    private BigDecimal dayIncomeGrowthRate; //最新增长率

    private BigDecimal currentRate; //当前收益率
    private BigDecimal accumIncomeRate; //累计收益率
    /**
     * 收益更新时间
     */
    private String updateDate;
    /**
     * 单位持仓成本去费
     */
    private BigDecimal unitBalanceCostExFee;
    /**
     * 单位持仓成本去费(人民币)
     */
    private BigDecimal unitBalanceCostExFeeRmb;
    private String incomeDt; //日期
    private String fundCode; //基金代码
    private String currencyUnit; //货币单位
    private BigDecimal accumCostNew;//投资总成本——新
    private BigDecimal accumCostRmbNew;//投资总成本——新
    private BigDecimal accumCollection; //累计总回款
    private BigDecimal accumCollectionRmb; //累计总回款(人民币)
    private BigDecimal balanceAmtExFee; //NA费后参考市值
    private BigDecimal balanceAmtExFeeRmb; //NA费后参考市值(人民币)

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public BigDecimal getUnitBalanceCostExFee() {
        return unitBalanceCostExFee;
    }

    public void setUnitBalanceCostExFee(BigDecimal unitBalanceCostExFee) {
        this.unitBalanceCostExFee = unitBalanceCostExFee;
    }

    public BigDecimal getUnitBalanceCostExFeeRmb() {
        return unitBalanceCostExFeeRmb;
    }

    public void setUnitBalanceCostExFeeRmb(BigDecimal unitBalanceCostExFeeRmb) {
        this.unitBalanceCostExFeeRmb = unitBalanceCostExFeeRmb;
    }

    public BigDecimal getBalanceAmtExFee() {
        return balanceAmtExFee;
    }

    public void setBalanceAmtExFee(BigDecimal balanceAmtExFee) {
        this.balanceAmtExFee = balanceAmtExFee;
    }

    public BigDecimal getBalanceAmtExFeeRmb() {
        return balanceAmtExFeeRmb;
    }

    public void setBalanceAmtExFeeRmb(BigDecimal balanceAmtExFeeRmb) {
        this.balanceAmtExFeeRmb = balanceAmtExFeeRmb;
    }

    public BigDecimal getAccumCollection() {
        return accumCollection;
    }

    public void setAccumCollection(BigDecimal accumCollection) {
        this.accumCollection = accumCollection;
    }

    public BigDecimal getAccumCollectionRmb() {
        return accumCollectionRmb;
    }

    public void setAccumCollectionRmb(BigDecimal accumCollectionRmb) {
        this.accumCollectionRmb = accumCollectionRmb;
    }

    public BigDecimal getAccumCostNew() {
        return accumCostNew;
    }

    public void setAccumCostNew(BigDecimal accumCostNew) {
        this.accumCostNew = accumCostNew;
    }

    public BigDecimal getAccumCostRmbNew() {
        return accumCostRmbNew;
    }

    public void setAccumCostRmbNew(BigDecimal accumCostRmbNew) {
        this.accumCostRmbNew = accumCostRmbNew;
    }

    public BigDecimal getCurrentAsset() {
        return currentAsset;
    }

    public void setCurrentAsset(BigDecimal currentAsset) {
        this.currentAsset = currentAsset;
    }

    public BigDecimal getCurrentAssetRmb() {
        return currentAssetRmb;
    }

    public void setCurrentAssetRmb(BigDecimal currentAssetRmb) {
        this.currentAssetRmb = currentAssetRmb;
    }

    public BigDecimal getBalanceCost() {
        return balanceCost;
    }

    public void setBalanceCost(BigDecimal balanceCost) {
        this.balanceCost = balanceCost;
    }

    public BigDecimal getBalanceCostRmb() {
        return balanceCostRmb;
    }

    public void setBalanceCostRmb(BigDecimal balanceCostRmb) {
        this.balanceCostRmb = balanceCostRmb;
    }

    public BigDecimal getDailyAsset() {
        return dailyAsset;
    }

    public void setDailyAsset(BigDecimal dailyAsset) {
        this.dailyAsset = dailyAsset;
    }

    public BigDecimal getDailyAssetRmb() {
        return dailyAssetRmb;
    }

    public void setDailyAssetRmb(BigDecimal dailyAssetRmb) {
        this.dailyAssetRmb = dailyAssetRmb;
    }

    public BigDecimal getAccumIncome() {
        return accumIncome;
    }

    public void setAccumIncome(BigDecimal accumIncome) {
        this.accumIncome = accumIncome;
    }

    public BigDecimal getAccumIncomeRmb() {
        return accumIncomeRmb;
    }

    public void setAccumIncomeRmb(BigDecimal accumIncomeRmb) {
        this.accumIncomeRmb = accumIncomeRmb;
    }

    public BigDecimal getAccumRealizedIncome() {
        return accumRealizedIncome;
    }

    public void setAccumRealizedIncome(BigDecimal accumRealizedIncome) {
        this.accumRealizedIncome = accumRealizedIncome;
    }

    public BigDecimal getAccumRealizedIncomeRmb() {
        return accumRealizedIncomeRmb;
    }

    public void setAccumRealizedIncomeRmb(BigDecimal accumRealizedIncomeRmb) {
        this.accumRealizedIncomeRmb = accumRealizedIncomeRmb;
    }

    public BigDecimal getAccumCost() {
        return accumCost;
    }

    public void setAccumCost(BigDecimal accumCost) {
        this.accumCost = accumCost;
    }

    public BigDecimal getAccumCostRmb() {
        return accumCostRmb;
    }

    public void setAccumCostRmb(BigDecimal accumCostRmb) {
        this.accumCostRmb = accumCostRmb;
    }

    public BigDecimal getBalanceAmt() {
        return balanceAmt;
    }

    public void setBalanceAmt(BigDecimal balanceAmt) {
        this.balanceAmt = balanceAmt;
    }

    public BigDecimal getBalanceAmtRmb() {
        return balanceAmtRmb;
    }

    public void setBalanceAmtRmb(BigDecimal balanceAmtRmb) {
        this.balanceAmtRmb = balanceAmtRmb;
    }

    public BigDecimal getReceivManageFee() {
        return receivManageFee;
    }

    public void setReceivManageFee(BigDecimal receivManageFee) {
        this.receivManageFee = receivManageFee;
    }

    public BigDecimal getReceivPreformFee() {
        return receivPreformFee;
    }

    public void setReceivPreformFee(BigDecimal receivPreformFee) {
        this.receivPreformFee = receivPreformFee;
    }

    public BigDecimal getBalanceIncomeNew() {
        return balanceIncomeNew;
    }

    public void setBalanceIncomeNew(BigDecimal balanceIncomeNew) {
        this.balanceIncomeNew = balanceIncomeNew;
    }

    public BigDecimal getBalanceIncomeNewRmb() {
        return balanceIncomeNewRmb;
    }

    public void setBalanceIncomeNewRmb(BigDecimal balanceIncomeNewRmb) {
        this.balanceIncomeNewRmb = balanceIncomeNewRmb;
    }

    public BigDecimal getAccumIncomeNew() {
        return accumIncomeNew;
    }

    public void setAccumIncomeNew(BigDecimal accumIncomeNew) {
        this.accumIncomeNew = accumIncomeNew;
    }

    public BigDecimal getAccumIncomeNewRmb() {
        return accumIncomeNewRmb;
    }

    public void setAccumIncomeNewRmb(BigDecimal accumIncomeNewRmb) {
        this.accumIncomeNewRmb = accumIncomeNewRmb;
    }

    public BigDecimal getBalanceFloatIncome() {
        return balanceFloatIncome;
    }

    public void setBalanceFloatIncome(BigDecimal balanceFloatIncome) {
        this.balanceFloatIncome = balanceFloatIncome;
    }

    public BigDecimal getBalanceFloatIncomeRmb() {
        return balanceFloatIncomeRmb;
    }

    public void setBalanceFloatIncomeRmb(BigDecimal balanceFloatIncomeRmb) {
        this.balanceFloatIncomeRmb = balanceFloatIncomeRmb;
    }

    public BigDecimal getBalanceFloatIncomeRate() {
        return balanceFloatIncomeRate;
    }

    public void setBalanceFloatIncomeRate(BigDecimal balanceFloatIncomeRate) {
        this.balanceFloatIncomeRate = balanceFloatIncomeRate;
    }

    public BigDecimal getDayAssetRate() {
        return dayAssetRate;
    }

    public void setDayAssetRate(BigDecimal dayAssetRate) {
        this.dayAssetRate = dayAssetRate;
    }

    public BigDecimal getDayIncomeGrowthRate() {
        return dayIncomeGrowthRate;
    }

    public void setDayIncomeGrowthRate(BigDecimal dayIncomeGrowthRate) {
        this.dayIncomeGrowthRate = dayIncomeGrowthRate;
    }

    public String getIncomeDt() {
        return incomeDt;
    }

    public void setIncomeDt(String incomeDt) {
        this.incomeDt = incomeDt;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getCurrencyUnit() {
        return currencyUnit;
    }

    public void setCurrencyUnit(String currencyUnit) {
        this.currencyUnit = currencyUnit;
    }

    public BigDecimal getCurrentRate() {
        return currentRate;
    }

    public void setCurrentRate(BigDecimal currentRate) {
        this.currentRate = currentRate;
    }

    public BigDecimal getAccumIncomeRate() {
        return accumIncomeRate;
    }

    public void setAccumIncomeRate(BigDecimal accumIncomeRate) {
        this.accumIncomeRate = accumIncomeRate;
    }
}
