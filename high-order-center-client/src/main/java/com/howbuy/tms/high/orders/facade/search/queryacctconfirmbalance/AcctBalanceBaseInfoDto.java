package com.howbuy.tms.high.orders.facade.search.queryacctconfirmbalance;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:用户持仓基础信息
 * @Author: yun.lu
 * Date: 2023/12/18 15:10
 */
@Data
public class AcctBalanceBaseInfoDto extends BaseDto {
    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 一账通账号
     */
    private String hbOneNo;

    /**
     * 产品编码
     */
    private String fundCode;

    /**
     * 持仓份额
     */
    private BigDecimal balanceVol;

    /**
     * 是否是香港产品
     */
    private String isHkProduct;

    /**
     * 分销渠道
     */
    private String disCode;

}
