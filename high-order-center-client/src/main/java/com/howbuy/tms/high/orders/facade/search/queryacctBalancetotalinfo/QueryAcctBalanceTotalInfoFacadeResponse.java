package com.howbuy.tms.high.orders.facade.search.queryacctBalancetotalinfo;

import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Data;

import java.util.List;

/**
 * @Description:查询用户账户信息结果
 * @Author: yun.lu
 * Date: 2023/8/17 16:23
 */
@Data
public class QueryAcctBalanceTotalInfoFacadeResponse extends OrderSearchBaseResponse {
    /**
     * 当前账户无持仓,1:有;0:没有
     */
    private String hasBalance;

    /**
     * 当前账户近1年内，有没有持仓,1:有;0:没有
     */
    private String balanceOneYear;

    /**
     * 当前账户近1年内，曾经持有过“特殊隐藏配置表”内的产品,1:有;0:没有
     */
    private String balanceHiddenConfFundOneYear;

    /**
     * 当前账户近1年内，曾经持有过的配置的持仓产品代码
     */
    private List<String> balanceConfigFundCodeList;

    /**
     * 当前账户近1年内，曾经持有过“特殊产品交易类型转译表”内的产品,1:有;0:没有
     */
    private String balanceTransferConfFundOneYear;

    /**
     * 当前账号是否有香港产品持仓,1:有;0:没有
     */
    private String hasHkProduct= YesOrNoEnum.NO.getCode();

    /**
     * 当前账号是否有好臻产品持仓,1:有;0:没有
     */
    private String hasHzProduct=YesOrNoEnum.NO.getCode();


}
