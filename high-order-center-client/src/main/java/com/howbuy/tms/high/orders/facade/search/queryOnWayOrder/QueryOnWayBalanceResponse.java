package com.howbuy.tms.high.orders.facade.search.queryOnWayOrder;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Data;

import java.util.List;

/**
 * @Description:在途订单信息
 * @Author: yun.lu
 * Date: 2023/11/27 15:00
 */
@Data
public class QueryOnWayBalanceResponse extends OrderSearchBaseResponse {
    /**
     * 在途持仓信息
     */
    List<OnWayAgentDealDtlInfo> onWayAgentDealDtlInfoList;
}
