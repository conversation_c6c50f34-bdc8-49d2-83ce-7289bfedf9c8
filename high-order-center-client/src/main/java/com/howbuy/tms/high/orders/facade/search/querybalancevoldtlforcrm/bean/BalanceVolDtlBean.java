/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.bean;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 请在此添加描述
 * <AUTHOR>
 * @date 2021/5/13 16:43
 * @since JDK 1.8
 */
public class BalanceVolDtlBean implements Serializable {

    private static final long serialVersionUID = 1805132509442832639L;

    /**
     * 持仓份额
     */
    private BigDecimal balanceVol;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 银行卡号（按照掩码规则只给后六位）
     */
    private String bankAcctMask;
    /**
     * 资金账号
     */
    private String cpAcctNo;
    /**
     * 可赎回日期
     */
    private String canRedeemDt;
    /**
     * 赎回开放日期（锁定到期日）
     */
    private String openRedeDt;

    public BigDecimal getBalanceVol() {
        return balanceVol;
    }

    public void setBalanceVol(BigDecimal balanceVol) {
        this.balanceVol = balanceVol;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAcctMask() {
        return bankAcctMask;
    }

    public void setBankAcctMask(String bankAcctMask) {
        this.bankAcctMask = bankAcctMask;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getCanRedeemDt() {
        return canRedeemDt;
    }

    public void setCanRedeemDt(String canRedeemDt) {
        this.canRedeemDt = canRedeemDt;
    }

    public String getOpenRedeDt() {
        return openRedeDt;
    }

    public void setOpenRedeDt(String openRedeDt) {
        this.openRedeDt = openRedeDt;
    }
}