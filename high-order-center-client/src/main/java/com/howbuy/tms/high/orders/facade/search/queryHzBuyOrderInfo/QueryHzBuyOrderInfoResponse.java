package com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description:好臻下单所需订单信息返回信息实体
 * @Author: yun.lu
 * Date: 2023/11/21 19:00
 */
@Data
public class QueryHzBuyOrderInfoResponse extends OrderSearchBaseResponse {
    /**
     * 认缴金额(只有分次call才有)
     */
    private BigDecimal subscribeAmt;

    /**
     * 实缴金额
     */
    private BigDecimal paidAmt;

    /**
     * 购买金额(购买金额只有非分次call才有)
     */
    private BigDecimal buyAmt;

    /**
     * 购买金额是否可以修改,1:可以;0:不可以
     */
    private String buyAmtCanUpdate;

    /**
     * 是否需要校验限额,1:需要;0:不需要
     */
    private String needCheckAmt;


    /**
     * 认缴金额是否可以修改,1:可以;0:不可以
     */
    private String subscribeAmtCanUpdate;

    /**
     * 是否首次实缴,1:是;0:不是
     */
    private String isFirstPay;

    /**
     * 银行卡号是否可以修改,1:可以;0:不可以
     */
    private String bankCardCanUpdate;

    /**
     * 计算手续费方式,4:按认缴金额,5:按实缴金额
     */
    private String feeRateMethod;


    /**
     * 认缴表中的认缴金额(只有分次call才有)
     */
    private BigDecimal confirmSubscribeAmt;

    /**
     * 已实缴金额
     */
    private BigDecimal confirmPaidAmt;

    /**
     * 是否分次CALL款股权产品 0-否 1是
     */
    private String peDivideCallFlag;
    /**
     * 查询日期
     */
    private Date queryDate;

    /**
     * 银行卡号信息
     */
    private List<CustomerBankCardInfo> customerBankCardInfoList;
}
