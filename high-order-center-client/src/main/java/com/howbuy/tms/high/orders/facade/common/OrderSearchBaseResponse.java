/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.common;



/**
 * @description:(查询基础响应)
 * @reason:
 * <AUTHOR>
 * @date 2017年4月11日 下午1:47:49
 * @since JDK 1.7
 */

/**
 * @apiDefine orderSearchBaseResponse 共有响应参数(res)
 * @apiGroup high-order-center
 */
public class OrderSearchBaseResponse extends OrderBaseResponse {

    private static final long serialVersionUID = 1366386455645632858L;

}
